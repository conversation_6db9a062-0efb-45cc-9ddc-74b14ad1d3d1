import { useParams, useNavigate } from 'react-router-dom';
import { useEffect } from 'react';
import { useAuthStore } from '../../store/useAuthStore';
import { useWishlistStore } from '../../store/useWishlistStore';
import darjeeling from '../../assets/photos/dargeeling2.webp';
import gangtok from '../../assets/photos/dooars.webp';
import northSikkim from '../../assets/photos/plan2.png';
import card2 from '../../assets/photos/card2.webp';

const PackageDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const { addToWishlist, removeFromWishlist, isInWishlist, initializeWishlist } = useWishlistStore();

  // Initialize wishlist when component mounts
  useEffect(() => {
    if (user) {
      initializeWishlist(user._id);
    }
  }, [user, initializeWishlist]);

  // Package data matching Plan.jsx
  const packages = [
    {
      id: 1,
      location: "Darjeeling",
      duration: "3D/2N",
      no_of_persons: "2",
      price: "₹15,999",
      image: darjeeling,
      itinerary: [
        {
          day: 1,
          title: "Arrival in Darjeeling",
          activities: [
            "Check-in at hotel",
            "Visit Mall Road",
            "Sunset at Observatory Hill",
            "Local market exploration"
          ]
        },
        {
          day: 2,
          title: "Darjeeling Sightseeing",
          activities: [
            "Tiger Hill sunrise",
            "Batasia Loop",
            "Ghum Monastery",
            "Tea Garden visit"
          ]
        },
        {
          day: 3,
          title: "Departure",
          activities: [
            "Hotel check-out",
            "Last minute shopping",
            "Departure to home destination"
          ]
        }
      ],
      included: [
        { icon: "fa-car", text: "Private Car" },
        { icon: "fa-hotel", text: "Accommodation" },
        { icon: "fa-eye", text: "Sightseeing" },
        { icon: "fa-headset", text: "24/7 Support" }
      ]
    },
    {
      id: 2,
      location: "Darjeeling-Gangtok",
      duration: "4D/3N",
      no_of_persons: "2",
      price: "₹18,999",
      image: gangtok,
      itinerary: [
        {
          day: 1,
          title: "Arrival in Darjeeling",
          activities: [
            "Check-in at hotel",
            "Local sightseeing",
            "Mall Road visit",
            "Evening at leisure"
          ]
        },
        {
          day: 2,
          title: "Darjeeling to Gangtok",
          activities: [
            "Tiger Hill sunrise",
            "Check-out and travel to Gangtok",
            "Check-in at Gangtok hotel",
            "MG Road exploration"
          ]
        },
        {
          day: 3,
          title: "Gangtok Sightseeing",
          activities: [
            "Tsomgo Lake visit",
            "Baba Mandir",
            "Ropeway ride",
            "Local market visit"
          ]
        },
        {
          day: 4,
          title: "Departure",
          activities: [
            "Hotel check-out",
            "Last minute shopping",
            "Departure to home destination"
          ]
        }
      ],
      included: [
        { icon: "fa-car", text: "Private Car" },
        { icon: "fa-hotel", text: "Accommodation" },
        { icon: "fa-eye", text: "Sightseeing" },
        { icon: "fa-headset", text: "24/7 Support" }
      ]
    },
    {
      id: 3,
      location: "Gangtok-North Sikkim",
      duration: "5D/4N",
      no_of_persons: "2",
      price: "₹22,999",
      image: northSikkim,
      itinerary: [
        {
          day: 1,
          title: "Arrival in Gangtok",
          activities: [
            "Check-in at hotel",
            "MG Road exploration",
            "Local market visit",
            "Evening at leisure"
          ]
        },
        {
          day: 2,
          title: "Gangtok Sightseeing",
          activities: [
            "Tsomgo Lake visit",
            "Baba Mandir",
            "Nathula Pass (if permits available)",
            "Return to Gangtok"
          ]
        },
        {
          day: 3,
          title: "Gangtok to North Sikkim",
          activities: [
            "Early departure to Lachung",
            "Scenic drive through mountains",
            "Check-in at Lachung",
            "Local exploration"
          ]
        },
        {
          day: 4,
          title: "North Sikkim Exploration",
          activities: [
            "Yumthang Valley visit",
            "Zero Point excursion",
            "Hot springs visit",
            "Return to Lachung"
          ]
        },
        {
          day: 5,
          title: "Return to Gangtok & Departure",
          activities: [
            "Check-out from Lachung",
            "Drive back to Gangtok",
            "Departure to home destination"
          ]
        }
      ],
      included: [
        { icon: "fa-car", text: "Private Car" },
        { icon: "fa-hotel", text: "Accommodation" },
        { icon: "fa-eye", text: "Sightseeing" },
        { icon: "fa-headset", text: "24/7 Support" }
      ]
    },
    {
      id: 4,
      location: "Darjeeling-Gangtok-North Sikkim",
      duration: "6D/5N",
      no_of_persons: "2",
      price: "₹25,999",
      image: card2,
      itinerary: [
        {
          day: 1,
          title: "Arrival in Darjeeling",
          activities: [
            "Check-in at hotel",
            "Mall Road visit",
            "Local sightseeing",
            "Evening at leisure"
          ]
        },
        {
          day: 2,
          title: "Darjeeling Sightseeing",
          activities: [
            "Tiger Hill sunrise",
            "Batasia Loop",
            "Ghum Monastery",
            "Tea Garden visit"
          ]
        },
        {
          day: 3,
          title: "Darjeeling to Gangtok",
          activities: [
            "Check-out from Darjeeling",
            "Drive to Gangtok",
            "Check-in at Gangtok hotel",
            "MG Road exploration"
          ]
        },
        {
          day: 4,
          title: "Gangtok to North Sikkim",
          activities: [
            "Early departure to Lachung",
            "Scenic mountain drive",
            "Check-in at Lachung",
            "Local exploration"
          ]
        },
        {
          day: 5,
          title: "North Sikkim Exploration",
          activities: [
            "Yumthang Valley visit",
            "Zero Point excursion",
            "Hot springs visit",
            "Return to Lachung"
          ]
        },
        {
          day: 6,
          title: "Return & Departure",
          activities: [
            "Check-out from Lachung",
            "Drive back to Gangtok",
            "Departure to home destination"
          ]
        }
      ],
      included: [
        { icon: "fa-car", text: "Private Car" },
        { icon: "fa-hotel", text: "Accommodation" },
        { icon: "fa-eye", text: "Sightseeing" },
        { icon: "fa-headset", text: "24/7 Support" }
      ]
    },
    {
      id: 5,
      location: "Darjeeling",
      duration: "3D/2N",
      no_of_persons: "3",
      price: "₹12,999",
      image: gangtok,
      itinerary: [
        {
          day: 1,
          title: "Arrival in Darjeeling",
          activities: [
            "Check-in at hotel",
            "Visit Mall Road",
            "Sunset at Observatory Hill",
            "Local market exploration"
          ]
        },
        {
          day: 2,
          title: "Darjeeling Sightseeing",
          activities: [
            "Tiger Hill sunrise",
            "Batasia Loop",
            "Ghum Monastery",
            "Tea Garden visit"
          ]
        },
        {
          day: 3,
          title: "Departure",
          activities: [
            "Hotel check-out",
            "Last minute shopping",
            "Departure to home destination"
          ]
        }
      ],
      included: [
        { icon: "fa-car", text: "Private Car" },
        { icon: "fa-hotel", text: "Accommodation" },
        { icon: "fa-eye", text: "Sightseeing" },
        { icon: "fa-headset", text: "24/7 Support" }
      ]
    },
    {
      id: 6,
      location: "Darjeeling-Gangtok",
      duration: "4D/3N",
      no_of_persons: "3",
      price: "₹18,999",
      image: northSikkim,
      itinerary: [
        {
          day: 1,
          title: "Arrival in Darjeeling",
          activities: [
            "Check-in at hotel",
            "Local sightseeing",
            "Mall Road visit",
            "Evening at leisure"
          ]
        },
        {
          day: 2,
          title: "Darjeeling to Gangtok",
          activities: [
            "Tiger Hill sunrise",
            "Check-out and travel to Gangtok",
            "Check-in at Gangtok hotel",
            "MG Road exploration"
          ]
        },
        {
          day: 3,
          title: "Gangtok Sightseeing",
          activities: [
            "Tsomgo Lake visit",
            "Baba Mandir",
            "Ropeway ride",
            "Local market visit"
          ]
        },
        {
          day: 4,
          title: "Departure",
          activities: [
            "Hotel check-out",
            "Last minute shopping",
            "Departure to home destination"
          ]
        }
      ],
      included: [
        { icon: "fa-car", text: "Private Car" },
        { icon: "fa-hotel", text: "Accommodation" },
        { icon: "fa-eye", text: "Sightseeing" },
        { icon: "fa-headset", text: "24/7 Support" }
      ]
    },
    {
      id: 7,
      location: "Gangtok-North Sikkim",
      duration: "5D/4N",
      no_of_persons: "3",
      price: "₹22,999",
      image: darjeeling,
      itinerary: [
        {
          day: 1,
          title: "Arrival in Gangtok",
          activities: [
            "Check-in at hotel",
            "MG Road exploration",
            "Local market visit",
            "Evening at leisure"
          ]
        },
        {
          day: 2,
          title: "Gangtok Sightseeing",
          activities: [
            "Tsomgo Lake visit",
            "Baba Mandir",
            "Nathula Pass (if permits available)",
            "Return to Gangtok"
          ]
        },
        {
          day: 3,
          title: "Gangtok to North Sikkim",
          activities: [
            "Early departure to Lachung",
            "Scenic drive through mountains",
            "Check-in at Lachung",
            "Local exploration"
          ]
        },
        {
          day: 4,
          title: "North Sikkim Exploration",
          activities: [
            "Yumthang Valley visit",
            "Zero Point excursion",
            "Hot springs visit",
            "Return to Lachung"
          ]
        },
        {
          day: 5,
          title: "Return to Gangtok & Departure",
          activities: [
            "Check-out from Lachung",
            "Drive back to Gangtok",
            "Departure to home destination"
          ]
        }
      ],
      included: [
        { icon: "fa-car", text: "Private Car" },
        { icon: "fa-hotel", text: "Accommodation" },
        { icon: "fa-eye", text: "Sightseeing" },
        { icon: "fa-headset", text: "24/7 Support" }
      ]
    },
    {
      id: 8,
      location: "Darjeeling-Gangtok-North Sikkim",
      duration: "6D/5N",
      no_of_persons: "3",
      price: "₹25,999",
      image: card2,
      itinerary: [
        {
          day: 1,
          title: "Arrival in Darjeeling",
          activities: [
            "Check-in at hotel",
            "Mall Road visit",
            "Local sightseeing",
            "Evening at leisure"
          ]
        },
        {
          day: 2,
          title: "Darjeeling Sightseeing",
          activities: [
            "Tiger Hill sunrise",
            "Batasia Loop",
            "Ghum Monastery",
            "Tea Garden visit"
          ]
        },
        {
          day: 3,
          title: "Darjeeling to Gangtok",
          activities: [
            "Check-out from Darjeeling",
            "Drive to Gangtok",
            "Check-in at Gangtok hotel",
            "MG Road exploration"
          ]
        },
        {
          day: 4,
          title: "Gangtok to North Sikkim",
          activities: [
            "Early departure to Lachung",
            "Scenic mountain drive",
            "Check-in at Lachung",
            "Local exploration"
          ]
        },
        {
          day: 5,
          title: "North Sikkim Exploration",
          activities: [
            "Yumthang Valley visit",
            "Zero Point excursion",
            "Hot springs visit",
            "Return to Lachung"
          ]
        },
        {
          day: 6,
          title: "Return & Departure",
          activities: [
            "Check-out from Lachung",
            "Drive back to Gangtok",
            "Departure to home destination"
          ]
        }
      ],
      included: [
        { icon: "fa-car", text: "Private Car" },
        { icon: "fa-hotel", text: "Accommodation" },
        { icon: "fa-eye", text: "Sightseeing" },
        { icon: "fa-headset", text: "24/7 Support" }
      ]
    },
    // Packages for 4 persons
    {
      id: 9,
      location: "Darjeeling",
      duration: "3D/2N",
      no_of_persons: "4",
      price: "₹14,999",
      image: northSikkim,
      itinerary: [
        {
          day: 1,
          title: "Arrival in Darjeeling",
          activities: [
            "Check-in at hotel",
            "Visit Mall Road",
            "Sunset at Observatory Hill",
            "Local market exploration"
          ]
        },
        {
          day: 2,
          title: "Darjeeling Sightseeing",
          activities: [
            "Tiger Hill sunrise",
            "Batasia Loop",
            "Ghum Monastery",
            "Tea Garden visit"
          ]
        },
        {
          day: 3,
          title: "Departure",
          activities: [
            "Hotel check-out",
            "Last minute shopping",
            "Departure to home destination"
          ]
        }
      ],
      included: [
        { icon: "fa-car", text: "Private Car" },
        { icon: "fa-hotel", text: "Accommodation" },
        { icon: "fa-eye", text: "Sightseeing" },
        { icon: "fa-headset", text: "24/7 Support" }
      ]
    },
    {
      id: 10,
      location: "Darjeeling-Gangtok",
      duration: "4D/3N",
      no_of_persons: "4",
      price: "₹18,999",
      image: darjeeling,
      itinerary: [
        {
          day: 1,
          title: "Arrival in Darjeeling",
          activities: [
            "Check-in at hotel",
            "Local sightseeing",
            "Mall Road visit",
            "Evening at leisure"
          ]
        },
        {
          day: 2,
          title: "Darjeeling to Gangtok",
          activities: [
            "Tiger Hill sunrise",
            "Check-out and travel to Gangtok",
            "Check-in at Gangtok hotel",
            "MG Road exploration"
          ]
        },
        {
          day: 3,
          title: "Gangtok Sightseeing",
          activities: [
            "Tsomgo Lake visit",
            "Baba Mandir",
            "Ropeway ride",
            "Local market visit"
          ]
        },
        {
          day: 4,
          title: "Departure",
          activities: [
            "Hotel check-out",
            "Last minute shopping",
            "Departure to home destination"
          ]
        }
      ],
      included: [
        { icon: "fa-car", text: "Private Car" },
        { icon: "fa-hotel", text: "Accommodation" },
        { icon: "fa-eye", text: "Sightseeing" },
        { icon: "fa-headset", text: "24/7 Support" }
      ]
    },
    {
      id: 11,
      location: "Gangtok-North Sikkim",
      duration: "5D/4N",
      no_of_persons: "4",
      price: "₹25,999",
      image: gangtok,
      itinerary: [
        {
          day: 1,
          title: "Arrival in Gangtok",
          activities: [
            "Check-in at hotel",
            "MG Road exploration",
            "Local market visit",
            "Evening at leisure"
          ]
        },
        {
          day: 2,
          title: "Gangtok Sightseeing",
          activities: [
            "Tsomgo Lake visit",
            "Baba Mandir",
            "Nathula Pass (if permits available)",
            "Return to Gangtok"
          ]
        },
        {
          day: 3,
          title: "Gangtok to North Sikkim",
          activities: [
            "Early departure to Lachung",
            "Scenic drive through mountains",
            "Check-in at Lachung",
            "Local exploration"
          ]
        },
        {
          day: 4,
          title: "North Sikkim Exploration",
          activities: [
            "Yumthang Valley visit",
            "Zero Point excursion",
            "Hot springs visit",
            "Return to Lachung"
          ]
        },
        {
          day: 5,
          title: "Return to Gangtok & Departure",
          activities: [
            "Check-out from Lachung",
            "Drive back to Gangtok",
            "Departure to home destination"
          ]
        }
      ],
      included: [
        { icon: "fa-car", text: "Private Car" },
        { icon: "fa-hotel", text: "Accommodation" },
        { icon: "fa-eye", text: "Sightseeing" },
        { icon: "fa-headset", text: "24/7 Support" }
      ]
    },
    {
      id: 12,
      location: "Darjeeling-Gangtok-North Sikkim",
      duration: "6D/5N",
      no_of_persons: "4",
      price: "₹28,999",
      image: card2,
      itinerary: [
        {
          day: 1,
          title: "Arrival in Darjeeling",
          activities: [
            "Check-in at hotel",
            "Mall Road visit",
            "Local sightseeing",
            "Evening at leisure"
          ]
        },
        {
          day: 2,
          title: "Darjeeling Sightseeing",
          activities: [
            "Tiger Hill sunrise",
            "Batasia Loop",
            "Ghum Monastery",
            "Tea Garden visit"
          ]
        },
        {
          day: 3,
          title: "Darjeeling to Gangtok",
          activities: [
            "Check-out from Darjeeling",
            "Drive to Gangtok",
            "Check-in at Gangtok hotel",
            "MG Road exploration"
          ]
        },
        {
          day: 4,
          title: "Gangtok to North Sikkim",
          activities: [
            "Early departure to Lachung",
            "Scenic mountain drive",
            "Check-in at Lachung",
            "Local exploration"
          ]
        },
        {
          day: 5,
          title: "North Sikkim Exploration",
          activities: [
            "Yumthang Valley visit",
            "Zero Point excursion",
            "Hot springs visit",
            "Return to Lachung"
          ]
        },
        {
          day: 6,
          title: "Return & Departure",
          activities: [
            "Check-out from Lachung",
            "Drive back to Gangtok",
            "Departure to home destination"
          ]
        }
      ],
      included: [
        { icon: "fa-car", text: "Private Car" },
        { icon: "fa-hotel", text: "Accommodation" },
        { icon: "fa-eye", text: "Sightseeing" },
        { icon: "fa-headset", text: "24/7 Support" }
      ]
    },
    // Packages for 4+ persons
    {
      id: 13,
      location: "Darjeeling",
      duration: "3D/2N",
      no_of_persons: "4+",
      price: "₹16,999",
      image: gangtok,
      itinerary: [
        {
          day: 1,
          title: "Arrival in Darjeeling",
          activities: [
            "Check-in at hotel",
            "Visit Mall Road",
            "Sunset at Observatory Hill",
            "Local market exploration"
          ]
        },
        {
          day: 2,
          title: "Darjeeling Sightseeing",
          activities: [
            "Tiger Hill sunrise",
            "Batasia Loop",
            "Ghum Monastery",
            "Tea Garden visit"
          ]
        },
        {
          day: 3,
          title: "Departure",
          activities: [
            "Hotel check-out",
            "Last minute shopping",
            "Departure to home destination"
          ]
        }
      ],
      included: [
        { icon: "fa-car", text: "Private Car" },
        { icon: "fa-hotel", text: "Accommodation" },
        { icon: "fa-eye", text: "Sightseeing" },
        { icon: "fa-headset", text: "24/7 Support" }
      ]
    },
    {
      id: 14,
      location: "Darjeeling-Gangtok",
      duration: "4D/3N",
      no_of_persons: "4+",
      price: "₹20,999",
      image: darjeeling,
      itinerary: [
        {
          day: 1,
          title: "Arrival in Darjeeling",
          activities: [
            "Check-in at hotel",
            "Local sightseeing",
            "Mall Road visit",
            "Evening at leisure"
          ]
        },
        {
          day: 2,
          title: "Darjeeling to Gangtok",
          activities: [
            "Tiger Hill sunrise",
            "Check-out and travel to Gangtok",
            "Check-in at Gangtok hotel",
            "MG Road exploration"
          ]
        },
        {
          day: 3,
          title: "Gangtok Sightseeing",
          activities: [
            "Tsomgo Lake visit",
            "Baba Mandir",
            "Ropeway ride",
            "Local market visit"
          ]
        },
        {
          day: 4,
          title: "Departure",
          activities: [
            "Hotel check-out",
            "Last minute shopping",
            "Departure to home destination"
          ]
        }
      ],
      included: [
        { icon: "fa-car", text: "Private Car" },
        { icon: "fa-hotel", text: "Accommodation" },
        { icon: "fa-eye", text: "Sightseeing" },
        { icon: "fa-headset", text: "24/7 Support" }
      ]
    },
    {
      id: 15,
      location: "Gangtok-North Sikkim",
      duration: "5D/4N",
      no_of_persons: "4+",
      price: "₹28,999",
      image: gangtok,
      itinerary: [
        {
          day: 1,
          title: "Arrival in Gangtok",
          activities: [
            "Check-in at hotel",
            "MG Road exploration",
            "Local market visit",
            "Evening at leisure"
          ]
        },
        {
          day: 2,
          title: "Gangtok Sightseeing",
          activities: [
            "Tsomgo Lake visit",
            "Baba Mandir",
            "Nathula Pass (if permits available)",
            "Return to Gangtok"
          ]
        },
        {
          day: 3,
          title: "Gangtok to North Sikkim",
          activities: [
            "Early departure to Lachung",
            "Scenic drive through mountains",
            "Check-in at Lachung",
            "Local exploration"
          ]
        },
        {
          day: 4,
          title: "North Sikkim Exploration",
          activities: [
            "Yumthang Valley visit",
            "Zero Point excursion",
            "Hot springs visit",
            "Return to Lachung"
          ]
        },
        {
          day: 5,
          title: "Return to Gangtok & Departure",
          activities: [
            "Check-out from Lachung",
            "Drive back to Gangtok",
            "Departure to home destination"
          ]
        }
      ],
      included: [
        { icon: "fa-car", text: "Private Car" },
        { icon: "fa-hotel", text: "Accommodation" },
        { icon: "fa-eye", text: "Sightseeing" },
        { icon: "fa-headset", text: "24/7 Support" }
      ]
    },
    {
      id: 16,
      location: "Darjeeling-Gangtok-North Sikkim",
      duration: "6D/5N",
      no_of_persons: "4+",
      price: "₹32,999",
      image: card2,
      itinerary: [
        {
          day: 1,
          title: "Arrival in Darjeeling",
          activities: [
            "Check-in at hotel",
            "Mall Road visit",
            "Local sightseeing",
            "Evening at leisure"
          ]
        },
        {
          day: 2,
          title: "Darjeeling Sightseeing",
          activities: [
            "Tiger Hill sunrise",
            "Batasia Loop",
            "Ghum Monastery",
            "Tea Garden visit"
          ]
        },
        {
          day: 3,
          title: "Darjeeling to Gangtok",
          activities: [
            "Check-out from Darjeeling",
            "Drive to Gangtok",
            "Check-in at Gangtok hotel",
            "MG Road exploration"
          ]
        },
        {
          day: 4,
          title: "Gangtok to North Sikkim",
          activities: [
            "Early departure to Lachung",
            "Scenic mountain drive",
            "Check-in at Lachung",
            "Local exploration"
          ]
        },
        {
          day: 5,
          title: "North Sikkim Exploration",
          activities: [
            "Yumthang Valley visit",
            "Zero Point excursion",
            "Hot springs visit",
            "Return to Lachung"
          ]
        },
        {
          day: 6,
          title: "Return & Departure",
          activities: [
            "Check-out from Lachung",
            "Drive back to Gangtok",
            "Departure to home destination"
          ]
        }
      ],
      included: [
        { icon: "fa-car", text: "Private Car" },
        { icon: "fa-hotel", text: "Accommodation" },
        { icon: "fa-eye", text: "Sightseeing" },
        { icon: "fa-headset", text: "24/7 Support" }
      ]
    }
  ];

  const packageData = packages.find(pkg => pkg.id === parseInt(id));

  if (!packageData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Package Not Found</h2>
          <button
            onClick={() => navigate('/plan')}
            className="bg-blue-400 text-white px-6 py-3 rounded-lg hover:bg-blue-500 transition-colors"
          >
            Back to Packages
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-24">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Back Button */}
        <button
          onClick={() => navigate("/plan")}
          className="flex items-center text-gray-600 hover:text-gray-800 mb-6 transition-colors"
        >
          <i className="fas fa-arrow-left mr-2"></i>
          Back to Packages
        </button>

        {/* Package Header */}
        <div className="bg-white rounded-xl shadow-lg overflow-hidden mb-8">
          <div className="p-6 border-b border-gray-200">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  {packageData.location}
                </h1>
                <div className="flex items-center text-gray-600 space-x-4">
                  <span className="flex items-center">
                    <i className="fas fa-clock mr-2"></i>
                    {packageData.duration}
                  </span>
                  <span className="flex items-center">
                    <i className="fas fa-users mr-2"></i>
                    {packageData.no_of_persons} Persons
                  </span>
                </div>
              </div>
              <div className="mt-4 md:mt-0">
                <div className="text-3xl font-bold text-blue-600">
                  {packageData.price}
                </div>
                <div className="text-sm text-gray-500">per person</div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Side - Package Image */}
          <div className="bg-white rounded-xl shadow-lg overflow-hidden">
            <div className="p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4">
                Package Overview
              </h2>
              <div className="aspect-w-16 aspect-h-12 mb-6">
                <img
                  src={packageData.image}
                  alt={packageData.location}
                  className="w-full h-80 object-cover rounded-lg"
                />
              </div>
              {/* Book Now & Contact Us Buttons */}
              <div className="mt-8 bg-white rounded-xl shadow-lg p-6">
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <button
                    onClick={() => navigate("/contact")}
                    className="bg-blue-400 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-500 transition-colors flex items-center justify-center"
                  >
                    <i className="fas fa-envelope mr-2"></i>
                    Book Now
                  </button>
                  <button
                    onClick={() => {
                      const packageId = parseInt(id);
                      if (isInWishlist(packageId)) {
                        removeFromWishlist(packageId, user?._id);
                      } else {
                        addToWishlist(packageId, user?._id);
                      }
                    }}
                    className={`px-8 py-3 rounded-lg font-semibold transition-colors flex items-center justify-center ${
                      isInWishlist(parseInt(id))
                        ? 'bg-red-500 text-white hover:bg-red-600'
                        : 'bg-white border border-blue-500 text-blue-500 hover:bg-indigo-50'
                    }`}
                  >
                    <i className={`fas fa-heart mr-2 ${isInWishlist(parseInt(id)) ? 'text-white' : ''}`}></i>
                    {isInWishlist(parseInt(id)) ? 'Remove from Wishlist' : 'Add to Wishlist'}
                  </button>
                </div>
              </div>

              {/* Included Items */}
              <div className="border-t border-gray-200 pt-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Included
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  {packageData.included.map((item, index) => (
                    <div
                      key={index}
                      className="flex items-center p-3 bg-gray-50 rounded-lg"
                    >
                      <div className="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center mr-3">
                        <i className={`fas ${item.icon} text-blue-500`}></i>
                      </div>
                      <span className="text-gray-700 font-medium">
                        {item.text}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Right Side - Itinerary Details */}
          <div className="bg-white rounded-xl shadow-lg overflow-hidden">
            <div className="p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-6">
                Itinerary Details
              </h2>

              <div className="space-y-6">
                {packageData.itinerary.map((day, index) => (
                  <div
                    key={index}
                    className="border-l-4 border-blue-500 pl-6 relative"
                  >
                    {/* Day Number Circle */}
                    <div className="absolute -left-3 top-0 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-bold">
                        {day.day}
                      </span>
                    </div>

                    <div className="pb-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-3">
                        Day {day.day} - {day.title}
                      </h3>
                      <ul className="space-y-2">
                        {day.activities.map((activity, actIndex) => (
                          <li key={actIndex} className="flex items-start">
                            <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                            <span className="text-gray-700">{activity}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
      </div>
    </div>
  );
};

export default PackageDetails;