import { create } from 'zustand';
import toast from 'react-hot-toast';

export const useWishlistStore = create((set, get) => ({
  wishlistItems: [],
  
  // Initialize wishlist from localStorage
  initializeWishlist: (userId) => {
    if (!userId) return;
    
    try {
      const savedWishlist = localStorage.getItem(`wishlist_${userId}`);
      if (savedWishlist) {
        const wishlistIds = JSON.parse(savedWishlist);
        set({ wishlistItems: wishlistIds });
      }
    } catch (error) {
      console.error('Error loading wishlist:', error);
    }
  },

  // Add item to wishlist
  addToWishlist: (packageId, userId) => {
    if (!userId) {
      toast.error('Please login to add items to wishlist');
      return false;
    }

    const { wishlistItems } = get();
    
    if (wishlistItems.includes(packageId)) {
      toast.error('Package already in wishlist');
      return false;
    }

    const updatedWishlist = [...wishlistItems, packageId];
    
    try {
      localStorage.setItem(`wishlist_${userId}`, JSON.stringify(updatedWishlist));
      set({ wishlistItems: updatedWishlist });
      toast.success('Package added to wishlist');
      return true;
    } catch (error) {
      console.error('Error adding to wishlist:', error);
      toast.error('Failed to add package to wishlist');
      return false;
    }
  },

  // Remove item from wishlist
  removeFromWishlist: (packageId, userId) => {
    if (!userId) return false;

    const { wishlistItems } = get();
    const updatedWishlist = wishlistItems.filter(id => id !== packageId);
    
    try {
      localStorage.setItem(`wishlist_${userId}`, JSON.stringify(updatedWishlist));
      set({ wishlistItems: updatedWishlist });
      toast.success('Package removed from wishlist');
      return true;
    } catch (error) {
      console.error('Error removing from wishlist:', error);
      toast.error('Failed to remove package from wishlist');
      return false;
    }
  },

  // Check if item is in wishlist
  isInWishlist: (packageId) => {
    const { wishlistItems } = get();
    return wishlistItems.includes(packageId);
  },

  // Clear wishlist
  clearWishlist: (userId) => {
    if (!userId) return;
    
    try {
      localStorage.removeItem(`wishlist_${userId}`);
      set({ wishlistItems: [] });
      toast.success('Wishlist cleared');
    } catch (error) {
      console.error('Error clearing wishlist:', error);
      toast.error('Failed to clear wishlist');
    }
  },

  // Get wishlist count
  getWishlistCount: () => {
    const { wishlistItems } = get();
    return wishlistItems.length;
  }
}));
